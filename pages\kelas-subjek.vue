<template>
  <!-- Loading State -->
  <SkeletonLoader v-if="loading" variant="table" />

  <div v-if="!loading" class="space-y-8">
    <!-- Page Header -->
    <UiCompositePageHeader title="Kelas & Subjek" subtitle="Urus maklumat kelas dan subjek yang diajar"
      icon="heroicons:academic-cap-solid">
      <template #actions>
        <UiBaseButton variant="primary" size="sm" sm:size="md" prepend-icon="heroicons:plus-solid" @click="openAddModal"
          class="flex-1 sm:flex-none">
          <span class="hidden sm:inline">Tambah Kelas & Subjek</span>
          <span class="sm:hidden">Tambah</span>
        </UiBaseButton>
      </template>
    </UiCompositePageHeader>

    <!-- Search and Filter Section -->
    <UiCompositeCard v-if="classSubjects.length > 0">
      <template #default>
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <UiBaseInput v-model="searchQuery" placeholder="Cari kelas atau subjek..."
              prepend-icon="heroicons:magnifying-glass-solid" @input="handleSearch" />
          </div>
          <UiBaseButton v-if="searchQuery" variant="outline" size="sm" @click="clearSearch"
            prepend-icon="heroicons:x-mark-solid">
            Kosongkan
          </UiBaseButton>
        </div>
      </template>
    </UiCompositeCard>

    <!-- Data Table -->
    <UiCompositeCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <UiBaseIcon name="heroicons:table-cells-solid" class="w-5 h-5 text-primary" />
            <h2 class="text-xl font-semibold">Senarai Kelas & Subjek</h2>
          </div>
          <div v-if="filteredClassSubjects.length > 0" class="text-sm text-gray-500 dark:text-gray-400">
            {{ filteredClassSubjects.length }} daripada {{ classSubjects.length }} rekod
          </div>
        </div>
      </template>
      <template #default>
        <!-- Table for desktop -->
        <div v-if="filteredClassSubjects.length > 0" class="hidden md:block overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Tahap
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Nama Kelas
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Subjek
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Singkatan
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Bil. Murid
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Tindakan
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
              <tr v-for="(item, index) in filteredClassSubjects" :key="`${item.class_id}-${item.subject_id}-${index}`">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ getClassLevelLabel(item.class_id) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ item.className }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ getSubjectName(item.subject_id) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ item.subject_abbreviation ?? '-' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ item.studentCount ?? '-' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                  <UiBaseTooltip text="Kemaskini">
                    <UiBaseIcon name="ph:pencil-simple" size="18px" @click="openEditModal(item)"
                      class="cursor-pointer text-primary hover:text-primary-focus dark:text-primary-dark dark:hover:text-primary-dark-focus p-1 rounded-md" />
                  </UiBaseTooltip>
                  <UiBaseTooltip text="Padam">
                    <UiBaseIcon name="ph:trash" size="18px" @click="openDeleteConfirmation(item)"
                      class="cursor-pointer text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-500 p-1 rounded-md" />
                  </UiBaseTooltip>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Cards for mobile -->
        <div v-if="filteredClassSubjects.length > 0" class="md:hidden space-y-4">
          <div v-for="(item, index) in filteredClassSubjects"
            :key="`mobile-${item.class_id}-${item.subject_id}-${index}`"
            class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="font-medium text-gray-900 dark:text-gray-100">{{ item.className }}</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ getClassLevelLabel(item.class_id) }}</p>
              </div>
              <div class="flex space-x-2">
                <UiBaseIcon name="ph:pencil-simple" size="18px" @click="openEditModal(item)"
                  class="cursor-pointer text-primary hover:text-primary-focus p-1 rounded-md" />
                <UiBaseIcon name="ph:trash" size="18px" @click="openDeleteConfirmation(item)"
                  class="cursor-pointer text-red-600 hover:text-red-700 p-1 rounded-md" />
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500 dark:text-gray-400">Subjek:</span>
                <p class="font-medium text-gray-900 dark:text-gray-100">{{ getSubjectName(item.subject_id) }}</p>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">Singkatan:</span>
                <p class="font-medium text-gray-900 dark:text-gray-100">{{ item.subject_abbreviation ?? '-' }}</p>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">Bil. Murid:</span>
                <p class="font-medium text-gray-900 dark:text-gray-100">{{ item.studentCount ?? '-' }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="filteredClassSubjects.length === 0 && classSubjects.length === 0" class="text-center py-12">
          <UiBaseIcon name="heroicons:academic-cap-solid" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Tiada kelas & subjek</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Mulakan dengan menambah kelas dan subjek pertama
            anda.</p>
          <div class="mt-6">
            <UiBaseButton variant="primary" @click="openAddModal" prepend-icon="heroicons:plus-solid">
              Tambah Kelas & Subjek
            </UiBaseButton>
          </div>
        </div>

        <!-- No Search Results -->
        <div v-if="filteredClassSubjects.length === 0 && classSubjects.length > 0" class="text-center py-12">
          <UiBaseIcon name="heroicons:magnifying-glass-solid" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Tiada keputusan ditemui</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Cuba ubah kata kunci carian anda.</p>
          <div class="mt-6">
            <UiBaseButton variant="outline" @click="clearSearch">
              Kosongkan Carian
            </UiBaseButton>
          </div>
        </div>
      </template>
    </UiCompositeCard>

    <!-- Add Modal -->
    <ClassSubjectModal :is-open="isAddModalOpen" :user-class-subjects="classSubjects" @update:is-open="closeAddModal"
      @save="handleAddSave" />

    <!-- Direct Edit Modal -->
    <ClassSubjectEditModal :is-open="isEditModalOpen" :initial-data="currentEditingItem"
      @update:is-open="closeEditModal" @save="handleEditSave" />

    <!-- Delete Confirmation Modal -->
    <UiCompositeDeleteConfirmationModal :is-open="isDeleteModalOpen"
      :title="deleteItem ? `Padam ${deleteItem.className}` : 'Padam Kelas & Subjek'"
      :confirmation-message="deleteItem ? `Adakah anda pasti ingin memadam kelas '${deleteItem.className}' untuk subjek '${getSubjectName(deleteItem.subject_id)}'?` : ''"
      warning-message="Tindakan ini tidak boleh dibatalkan." item-type="kelas & subjek"
      :item-name="deleteItem?.className" @confirm="handleDelete" @cancel="closeDeleteConfirmation"
      :loading="isDeleting" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useSupabaseUser, useSupabaseClient } from '#imports'
import { useToast } from '~/composables/useToast'
import { useSubjects } from '~/composables/useSubjects'
import type { UserClassSubjectEntry } from '~/schemas/userSchemas'

// Components
import SkeletonLoader from '~/components/ui/skeleton/SkeletonLoader.vue'
import UiCompositePageHeader from '~/components/ui/composite/PageHeader.vue'
import UiCompositeCard from '~/components/ui/composite/Card.vue'
import UiBaseButton from '~/components/ui/base/Button.vue'
import UiBaseIcon from '~/components/ui/base/Icon.vue'
import UiBaseInput from '~/components/ui/base/Input.vue'
import UiBaseTooltip from '~/components/ui/base/Tooltip.vue'
import UiCompositeDeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue'
import ClassSubjectModal from '~/components/schedule/ClassSubjectModal.vue'
import ClassSubjectEditModal from '~/components/ui/composite/ClassSubjectEditModal.vue'

// Define page meta
definePageMeta({
  layout: 'default'
})

// Set page head
useHead({
  title: 'Kelas & Subjek - EduPlan Pro',
  meta: [
    {
      name: 'description',
      content: 'Urus maklumat kelas dan subjek yang diajar untuk perancangan yang lebih baik.'
    }
  ]
})

// =====================================================
// COMPOSABLES & STATE
// =====================================================

const user = useSupabaseUser()
const client = useSupabaseClient()
const { success: showSuccessToast, error: showErrorToast } = useToast()
const { subjects, fetchSubjects } = useSubjects()

// =====================================================
// REACTIVE STATE
// =====================================================

const loading = ref(true)
const classSubjects = ref<UserClassSubjectEntry[]>([])
const searchQuery = ref('')

// Add modal state
const isAddModalOpen = ref(false)

// Edit modal state
const isEditModalOpen = ref(false)
const currentEditingItem = ref<UserClassSubjectEntry | null>(null)
const isSaving = ref(false)

// Delete confirmation state
const isDeleteModalOpen = ref(false)
const deleteItem = ref<UserClassSubjectEntry | null>(null)
const isDeleting = ref(false)

// =====================================================
// COMPUTED PROPERTIES
// =====================================================

const filteredClassSubjects = computed(() => {
  if (!searchQuery.value.trim()) {
    return classSubjects.value
  }

  const query = searchQuery.value.toLowerCase().trim()
  return classSubjects.value.filter(item => {
    const className = item.className?.toLowerCase() || ''
    const subjectName = getSubjectName(item.subject_id)?.toLowerCase() || ''
    const abbreviation = item.subject_abbreviation?.toLowerCase() || ''

    return className.includes(query) ||
      subjectName.includes(query) ||
      abbreviation.includes(query)
  })
})

// =====================================================
// HELPER FUNCTIONS
// =====================================================

const getClassLevelLabel = (classId: string): string => {
  if (!classId) return '-'

  if (classId.startsWith('t')) {
    const level = classId.substring(1)
    return `Tahun ${level}`
  } else if (classId.startsWith('f')) {
    const level = classId.substring(1)
    return `Tingkatan ${level}`
  }

  return classId
}

const getSubjectName = (subjectId: string | null): string => {
  if (!subjectId) return '-'

  const subject = subjects.value.find(s => s.id === subjectId)
  return subject?.name || 'Subjek Tidak Diketahui'
}

// =====================================================
// DATA MANAGEMENT
// =====================================================

const fetchClassSubjects = async () => {
  if (!user.value) return

  try {
    loading.value = true

    // Use the same pattern as useSubjects.ts
    const { data: profileData, error: profileError } = await (client as any)
      .from('profiles')
      .select('class_subjects')
      .eq('id', user.value.id)
      .single()

    if (profileError) throw profileError

    if (profileData?.class_subjects) {
      // Handle different possible types for class_subjects (same as useSubjects.ts)
      let classSubjectsData: any[] = []
      if (Array.isArray(profileData.class_subjects)) {
        classSubjectsData = profileData.class_subjects
      } else if (profileData.class_subjects && typeof profileData.class_subjects === 'object') {
        // Convert object to array if it's a single object
        classSubjectsData = [profileData.class_subjects]
      }

      classSubjects.value = classSubjectsData as UserClassSubjectEntry[]

      // Fetch subject details for all subjects
      const subjectIds = classSubjects.value
        .map(cs => cs.subject_id)
        .filter(Boolean) as string[]

      if (subjectIds.length > 0) {
        await fetchSubjects(subjectIds)
      }
    }
  } catch (err) {
    console.error('Error fetching class subjects:', err)
    showErrorToast('Gagal memuatkan data kelas & subjek')
  } finally {
    loading.value = false
  }
}

const saveClassSubjects = async (data: UserClassSubjectEntry[]) => {
  if (!user.value) return false

  try {
    isSaving.value = true

    // Use the same pattern as other parts of the codebase
    const { error } = await (client as any)
      .from('profiles')
      .update({ class_subjects: data })
      .eq('id', user.value.id)

    if (error) throw error

    classSubjects.value = [...data]
    showSuccessToast('Kelas & subjek berjaya disimpan')
    return true
  } catch (err) {
    console.error('Error saving class subjects:', err)
    showErrorToast('Gagal menyimpan kelas & subjek')
    return false
  } finally {
    isSaving.value = false
  }
}

// =====================================================
// MODAL MANAGEMENT
// =====================================================

// Add modal functions
const openAddModal = () => {
  isAddModalOpen.value = true
}

const closeAddModal = () => {
  isAddModalOpen.value = false
}

const handleAddSave = async (data: UserClassSubjectEntry[]) => {
  const success = await saveClassSubjects(data)
  if (success) {
    closeAddModal()
  }
}

// Edit modal functions
const openEditModal = (item: UserClassSubjectEntry) => {
  currentEditingItem.value = { ...item }
  isEditModalOpen.value = true
}

const closeEditModal = () => {
  isEditModalOpen.value = false
  currentEditingItem.value = null
}

const handleEditSave = async (updatedItem: UserClassSubjectEntry) => {
  // Update the specific item in the classSubjects array
  const updatedClassSubjects = classSubjects.value.map(item => {
    if (item.class_id === currentEditingItem.value!.class_id &&
      item.subject_id === currentEditingItem.value!.subject_id) {
      return updatedItem
    }
    return item
  })

  const success = await saveClassSubjects(updatedClassSubjects)
  if (success) {
    closeEditModal()
  }
}

// =====================================================
// DELETE FUNCTIONALITY
// =====================================================

const openDeleteConfirmation = (item: UserClassSubjectEntry) => {
  deleteItem.value = item
  isDeleteModalOpen.value = true
}

const closeDeleteConfirmation = () => {
  deleteItem.value = null
  isDeleteModalOpen.value = false
}

const handleDelete = async () => {
  if (!deleteItem.value) return

  try {
    isDeleting.value = true

    const updatedClassSubjects = classSubjects.value.filter(item =>
      !(item.class_id === deleteItem.value!.class_id &&
        item.subject_id === deleteItem.value!.subject_id)
    )

    const success = await saveClassSubjects(updatedClassSubjects)
    if (success) {
      showSuccessToast('Kelas & subjek berjaya dipadam')
      closeDeleteConfirmation()
    }
  } catch (err) {
    console.error('Error deleting class subject:', err)
    showErrorToast('Gagal memadam kelas & subjek')
  } finally {
    isDeleting.value = false
  }
}

// =====================================================
// SEARCH FUNCTIONALITY
// =====================================================

const handleSearch = () => {
  // Search is reactive through computed property
}

const clearSearch = () => {
  searchQuery.value = ''
}

// =====================================================
// LIFECYCLE
// =====================================================

onMounted(async () => {
  await fetchClassSubjects()
})

// Watch for user changes
watch(user, async (newUser) => {
  if (newUser) {
    await fetchClassSubjects()
  } else {
    classSubjects.value = []
  }
})
</script>
