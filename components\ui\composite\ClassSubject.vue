<template>
    <div>
        <div class="flex justify-start">
            <Button @click="openModalForNew" variant="primary" type="button">
                Tambah Kelas & Subjek
            </Button>
        </div>

        <Modal ref="modalRef" :is-open="isModalOpen" :title="modalTitle" @update:is-open="isModalOpen = $event">
            <form @submit.prevent="saveClassSubject" class="space-y-4 p-4">
                <div class="flex space-x-2 mb-4">
                    <Button @click="selectLevelType('Tahun')"
                        :variant="selectedLevelType === 'Tahun' ? 'primary' : 'outline'" type="button" class="flex-1">
                        Tahun
                    </Button>
                    <Button @click="selectLevelType('Tingkatan')"
                        :variant="selectedLevelType === 'Tingkatan' ? 'primary' : 'outline'" type="button"
                        class="flex-1">
                        Tingkatan
                    </Button>
                </div>
                <div>
                    <SingleSelect id="class-select" v-model="formData.class" :options="classOptions"
                        option-label="label" option-value="value" placeholder="Sila pilih kelas"
                        @update:modelValue="handleClassSelection" :aria-invalid="!!formErrors?.class"
                        aria-describedby="class-error" :disabled="isFormDisabled" />
                    <span v-if="formErrors?.class" id="class-error" class="text-alert-error text-sm mt-1">{{
                        formErrors.class[0] }}</span>
                </div>

                <div v-if="formData.class && existingClassOptions.length > 0">
                    <SingleSelect id="existing-class-select" v-model="selectedExistingClassName"
                        :options="existingClassOptions" option-label="label" option-value="value"
                        placeholder="Pilih nama kelas sedia ada (jika ada)"
                        @update:modelValue="handleExistingClassSelection"
                        :disabled="isFormDisabled || !formData.class" />
                </div>

                <div>
                    <Input id="class-name-input" v-model="formData.className"
                        placeholder="Nama Kelas (Cth: Tahun 1, 2 Bestari)" @update:modelValue="handleClassNameInput"
                        :aria-invalid="!!formErrors?.className" aria-describedby="className-error"
                        :disabled="isFormDisabled" />
                    <span v-if="formErrors?.className" id="className-error" class="text-alert-error text-sm mt-1">{{
                        formErrors.className[0] }}</span>
                </div>

                <div>
                    <Input id="student-count-input" v-model.number="formData.studentCount" type="number"
                        placeholder="Jumlah Murid" @update:modelValue="handleStudentCountInput"
                        :aria-invalid="!!formErrors?.studentCount" aria-describedby="studentCount-error"
                        :disabled="isFormDisabled" />
                    <span v-if="formErrors?.studentCount" id="studentCount-error"
                        class="text-alert-error text-sm mt-1">{{
                            formErrors.studentCount[0] }}</span>
                </div>

                <div>
                    <SingleSelect id="subject-select" v-model="formData.subject" :options="computedSubjectOptions"
                        option-label="label" option-value="value" show-search
                        :placeholder="!formData.class ? 'Pilih Kelas dahulu' : 'Sila pilih subjek'"
                        @dropdown-did-open="handleDropdownDidOpen" @dropdown-state-changed="handleDropdownStateChange"
                        @update:modelValue="handleSubjectSelection" :aria-invalid="!!formErrors?.subject"
                        aria-describedby="subject-error"
                        :disabled="isFormDisabled || subjectsLoading || !formData.class">
                        <template #customOptionLabel="{ option }">
                            <div v-if="option.value === ADD_NEW_SUBJECT_VALUE" class="flex items-center">
                                <Icon name="mdi:plus" class="mr-2 h-5 w-5" />
                                <span>Tambah Subjek Baru...</span>
                            </div>
                        </template>
                    </SingleSelect>
                    <span v-if="formErrors?.subject && formData.subject !== ADD_NEW_SUBJECT_VALUE" id="subject-error"
                        class="text-alert-error text-sm mt-1">{{
                            formErrors.subject[0] }}</span>
                </div>

                <div v-if="showNewSubjectInput" class="space-y-2 mt-2">
                    <Input id="new-subject-input" v-model="newSubjectNameFromInput" placeholder="Taip nama subjek baru"
                        :disabled="isFormDisabled || subjectsLoading" />
                    <Button @click="addNewSubject" variant="secondary" type="button"
                        :disabled="isFormDisabled || subjectsLoading || !newSubjectNameFromInput.trim()">
                        Simpan Subjek Baru
                    </Button>
                    <span v-if="formErrors?.subject && formData.subject === ADD_NEW_SUBJECT_VALUE"
                        id="new-subject-error" class="text-alert-error text-sm mt-1">{{ formErrors.subject[0] }}</span>
                </div>

                <div>
                    <Input id="subject-abbreviation-input" v-model="formData.subjectAbbreviation"
                        placeholder="Singkatan nama subjek (cth: BM, BI, MAT)"
                        @update:modelValue="handleSubjectAbbreviationInput"
                        :aria-invalid="!!formErrors?.subjectAbbreviation" aria-describedby="subjectAbbreviation-error"
                        :disabled="isFormDisabled" />
                    <span v-if="formErrors?.subjectAbbreviation" id="subjectAbbreviation-error"
                        class="text-alert-error text-sm mt-1">{{
                            formErrors.subjectAbbreviation[0] }}</span>
                </div>

                <div class="flex flex-col-reverse gap-y-2 sm:flex-row sm:justify-end sm:gap-y-0 sm:space-x-3 mt-6">
                    <Button type="button" @click="cancelModal" variant="outline">
                        Batal
                    </Button>
                    <Button type="submit" variant="primary"
                        :disabled="isFormDisabled || subjectsLoading || !selectedLevelType">
                        {{ isEditing ? 'Kemaskini' : 'Simpan' }}
                    </Button>
                </div>
            </form>
        </Modal>

        <div v-if="savedClassSubjects.length > 0" class="mt-8">
            <div class="overflow-x-auto shadow-md sm:rounded-lg dark:bg-gray-800">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Tahap
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Nama Kelas
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Subjek
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Singkatan
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Bil. Murid
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">Tindakan</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                        <tr v-for="item in displaySavedClassSubjects" :key="item.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ getClassLevelLabel(item.class) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ item.className }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <span v-if="item.isSubjectLoading" class="text-gray-500 italic">Memuatkan...</span>
                                <span v-else>{{ item.subjectName }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ item.subjectAbbreviation ?? '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ item.studentCount ?? '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <Tooltip text="Kemaskini">
                                    <Icon name="ph:pencil-simple" size="18px" @click="handleEdit(item)"
                                        class="cursor-pointer text-primary hover:text-primary-focus dark:text-primary-dark dark:hover:text-primary-dark-focus p-1 rounded-md" />
                                </Tooltip>
                                <Tooltip text="Padam">
                                    <Icon name="ph:trash" size="18px" @click="handleDelete(item.id)"
                                        class="cursor-pointer text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-500 p-1 rounded-md" />
                                </Tooltip>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div v-else class="mt-8 text-center text-gray-500 dark:text-gray-400">
            Tiada data kelas dan subjek. Sila tambah.
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import SingleSelect from '@/components/ui/base/SingleSelect.vue';
import Input from '@/components/ui/base/Input.vue';
import Button from '@/components/ui/base/Button.vue';
import Icon from '@/components/ui/base/Icon.vue';
import Tooltip from '@/components/ui/base/Tooltip.vue';
import Modal from '@/components/ui/composite/Modal.vue';
import { z } from 'zod';
import { useSubjects, type Subject } from '@/composables/useSubjects';

// --- Type Definitions ---
interface FormOption {
    value: string; // Can be UUID string or ADD_NEW_SUBJECT_VALUE
    label: string;
    code?: string;
    disabled?: boolean; // For category headers
}

interface ClassSubjectDataBase {
    class: string | null; // e.g., 't1', 'f3' (represents class_level_id)
    className: string;    // e.g., "Tahun 1 Amanah", "3 Bestari"
    studentCount: number | null | undefined; // Allow undefined here
    subject: string | null; // Stores subject_id as string, or ADD_NEW_SUBJECT_VALUE
    subjectAbbreviation: string; // New field for subject abbreviation
}

// Internal representation for the list and form editing
interface InternalClassSubject extends ClassSubjectDataBase {
    id: number; // Internal temporary ID for list management
}

// Structure for data emitted to parent (matches userSchemas)
interface EmittedClassSubject {
    class_id: string; // 't1', 'f1' etc.
    className: string;
    subject_id: string | null; // Changed to string for UUID, now allowing null for session-only
    studentCount?: number | null | undefined; // Make studentCount optional
    subject_abbreviation?: string; // Make optional for backward compatibility with existing data
    // id?: string; // Optional: if parent needs to track by a specific ID from DB (UUID)
}


// Form data structure
interface ClassSubjectEntryForm extends ClassSubjectDataBase {
    id: number | null; // Used when editing an existing InternalClassSubject
}
// --- End Type Definitions ---

// Removed unused supabase and user imports

const props = defineProps({
    initialData: {
        type: Array as () => EmittedClassSubject[], // Expecting data that matches userSchemas
        default: () => []
    },
});

const emit = defineEmits(['update:data', 'data-invalid', 'data-valid']);

// Modal and Form State
const isModalOpen = ref(false);
const modalRef = ref<InstanceType<typeof Modal> | null>(null);
const editingId = ref<number | null>(null); // Tracks the internal ID of the item being edited
const isEditing = ref(false);
const selectedLevelType = ref<'Tahun' | 'Tingkatan' | null>(null);

const formData = ref<ClassSubjectEntryForm>({
    id: null,
    class: null,
    className: '',
    studentCount: null,
    subject: null,
    subjectAbbreviation: '',
});

// Subject related state
const ADD_NEW_SUBJECT_VALUE = 'add_new_subject_option_value'; // Unique string key
const newSubjectNameFromInput = ref('');
const showNewSubjectInput = ref(false);
const { subjects, loading: subjectsLoading, fetchSubjects, addSubject: addSubjectToDb } = useSubjects();

// Store session-only subjects locally within this component instance
const sessionOnlySubjects = ref<Subject[]>([]);

// "Kelas sedia ada" state
const selectedExistingClassName = ref<string | null>(null);

// Table data (internal list using temporary IDs)
const savedClassSubjects = ref<InternalClassSubject[]>([]);
let nextInternalId = 1; // Simple ID generator for local list

onMounted(async () => {
    await fetchSubjects(); // Fetch all subjects

    if (props.initialData && props.initialData.length > 0) {
        // Transform initialData (EmittedClassSubject) to InternalClassSubject for local list
        savedClassSubjects.value = props.initialData.map(item => ({
            id: nextInternalId++, // Assign a new internal ID
            class: item.class_id,
            className: item.className,
            studentCount: item.studentCount,
            subject: item.subject_id, // subject_id is already a string (UUID)
            subjectAbbreviation: (item as any).subject_abbreviation || '' // Handle old data that might not have this field
        }));
    }
});


// Zod Validation Schema
const ClassSubjectEntrySchema = z.object({
    id: z.number().nullable().optional(), // Internal ID, not strictly part of user data validation
    class: z.string({ required_error: "Sila pilih kelas.", invalid_type_error: "Sila pilih kelas." }).min(1, "Sila pilih kelas."),
    className: z.string({ required_error: "Nama kelas diperlukan." }).min(1, "Nama kelas diperlukan.").max(100, "Nama kelas terlalu panjang."),
    studentCount: z.number({ required_error: "Jumlah murid diperlukan.", invalid_type_error: "Jumlah murid mesti nombor." }).int("Jumlah murid mesti nombor bulat.").positive("Jumlah murid mesti nombor positif."),
    subject: z.string({ required_error: "Sila pilih subjek.", invalid_type_error: "Sila pilih subjek." }).min(1, "Sila pilih subjek.").refine(value => value !== ADD_NEW_SUBJECT_VALUE, {
        message: "Sila simpan subjek baru atau pilih subjek sedia ada.",
    }),
    subjectAbbreviation: z.string({ required_error: "Singkatan nama subjek diperlukan.", invalid_type_error: "Singkatan nama subjek mesti rentetan." }).min(1, "Singkatan nama subjek diperlukan.").max(10, "Singkatan nama subjek terlalu panjang (maksimum 10 aksara)."),
});

const formErrors = ref<Record<keyof Omit<ClassSubjectEntryForm, 'id'>, string[] | undefined> | null>(null);


// Options for Tahun/Tingkatan
const tahunOptions: FormOption[] = [
    { value: 't1', label: 'Tahun 1' }, { value: 't2', label: 'Tahun 2' }, { value: 't3', label: 'Tahun 3' },
    { value: 't4', label: 'Tahun 4' }, { value: 't5', label: 'Tahun 5' }, { value: 't6', label: 'Tahun 6' },
];
const tingkatanOptions: FormOption[] = [
    { value: 'f1', label: 'Tingkatan 1' }, { value: 'f2', label: 'Tingkatan 2' }, { value: 'f3', label: 'Tingkatan 3' },
    { value: 'f4', label: 'Tingkatan 4' }, { value: 'f5', label: 'Tingkatan 5' }, { value: 'f6', label: 'Tingkatan 6' },
];

// Category labels for better UX
const CATEGORY_LABELS: Record<string, string> = {
    'teras': 'Teras',
    'tambahan': 'Tambahan',
    'pilihan': 'Pilihan',
    'elektif_sains': 'Elektif Sains',
    'elektif_sastera': 'Elektif Sastera'
};

// Helper function to get class level from class value
const getClassLevel = (classValue: string): string | null => {
    if (!classValue) return null;
    if (classValue.startsWith('t')) return 'tahun';
    if (classValue.startsWith('f')) {
        const level = parseInt(classValue.substring(1));
        if (level >= 1 && level <= 3) return 'lower';
        if (level >= 4 && level <= 6) return 'upper'; // Fixed: Include Tingkatan 6 as upper level
    }
    return null;
};

const classOptions = computed<FormOption[]>(() => {
    if (selectedLevelType.value === 'Tahun') return tahunOptions;
    if (selectedLevelType.value === 'Tingkatan') return tingkatanOptions;
    return [];
});

const computedSubjectOptions = computed<FormOption[]>(() => {
    // Combine subjects from DB (via useSubjects) and session-only subjects
    let combinedSubjects = [...subjects.value, ...sessionOnlySubjects.value];

    // Filter subjects based on selected level and class
    if (selectedLevelType.value && formData.value.class) {
        const levelType = selectedLevelType.value.toLowerCase();
        const classLevel = getClassLevel(formData.value.class);

        combinedSubjects = combinedSubjects.filter(subject => {
            // Include custom subjects
            if (subject.is_custom) return true;

            // Filter by level_type
            if (subject.level_type !== levelType && subject.level_type !== 'both') return false;

            // For tingkatan, also filter by sub_level
            if (levelType === 'tingkatan' && classLevel && subject.sub_level) {
                if (subject.sub_level !== 'all' && subject.sub_level !== classLevel) return false;
            }

            return subject.is_active !== false; // Include if is_active is true or undefined
        });
    }

    // Group subjects by category and sort
    const groupedSubjects = combinedSubjects.reduce((groups, subject) => {
        const category = subject.category || 'lain-lain';
        if (!groups[category]) groups[category] = [];
        groups[category].push(subject);
        return groups;
    }, {} as Record<string, typeof combinedSubjects>);

    // Sort subjects within each category by sort_order then name
    Object.keys(groupedSubjects).forEach(category => {
        groupedSubjects[category].sort((a, b) => {
            if (a.sort_order !== b.sort_order) {
                return (a.sort_order || 999) - (b.sort_order || 999);
            }
            return a.name.localeCompare(b.name);
        });
    });

    // Create options with category headers
    const options: FormOption[] = [];
    const categoryOrder = ['teras', 'tambahan', 'pilihan', 'elektif_sains', 'elektif_sastera', 'lain-lain'];

    categoryOrder.forEach(category => {
        if (groupedSubjects[category] && groupedSubjects[category].length > 0) {
            // Add category header (disabled option)
            options.push({
                value: `header_${category}`,
                label: `--- ${CATEGORY_LABELS[category] || category.toUpperCase()} ---`,
                code: '',
                disabled: true
            });

            // Add subjects in this category
            groupedSubjects[category].forEach(subject => {
                options.push({
                    value: subject.id,
                    label: subject.name,
                    code: subject.code
                });
            });
        }
    });

    // Add "Tambah Subjek Baru..." option at the end
    if (options.length > 0) {
        options.push({ value: ADD_NEW_SUBJECT_VALUE, label: '+ Tambah Subjek Baru...', code: '' });
    }

    return options;
});

// Computed property for displaying saved class subjects with proper subject names
const displaySavedClassSubjects = computed(() => {
    return savedClassSubjects.value.map(item => ({
        ...item,
        subjectName: getSubjectNameById(item.subject),
        isSubjectLoading: subjectsLoading.value
    }));
});

// Auto-fill lookup system for subject abbreviations
const subjectAbbreviationLookup = computed<Map<string, string>>(() => {
    const lookup = new Map<string, string>();

    // From initial data (user's existing profile data)
    if (props.initialData && props.initialData.length > 0) {
        props.initialData.forEach(item => {
            if (item.subject_id && (item as any).subject_abbreviation) {
                lookup.set(item.subject_id, (item as any).subject_abbreviation);
            }
        });
    }

    // From current session entries (override with more recent entries)
    savedClassSubjects.value.forEach(item => {
        if (item.subject && item.subjectAbbreviation && !item.subject.startsWith('session_')) {
            lookup.set(item.subject, item.subjectAbbreviation);
        }
    });

    return lookup;
});

const existingClassOptions = computed<FormOption[]>(() => {
    if (!formData.value.class) return [];
    const selectedClassValue = formData.value.class;
    // Filter unique class names for the selected class level from the current list
    const uniqueClassNames = new Set(
        savedClassSubjects.value
            .filter(item => item.class === selectedClassValue && item.className)
            .map(item => item.className)
    );
    return Array.from(uniqueClassNames).map(name => ({ value: name, label: name }));
});


// UI State & Event Handlers
const isFormDisabled = computed(() => !selectedLevelType.value || subjectsLoading.value);
const modalTitle = computed(() => {
    if (!selectedLevelType.value) return 'Sila Pilih Tahun/Tingkatan Dahulu';
    return isEditing.value ? 'Kemaskini Kelas & Subjek' : 'Tambah Kelas & Subjek';
});

const isAdvancedSelectDropdownOpen = ref(false); // For SingleSelect subject dropdown state

const handleDropdownDidOpen = (payload: any) => {
    // Only auto-scroll for subject dropdown
    if (isAdvancedSelectDropdownOpen.value && modalRef.value && payload && payload.actualHeight) {
        // Wait for dropdown to render, then scroll modal to bottom
        nextTick(() => {
            const modalScrollArea = modalRef.value?.modalScrollArea;
            if (modalScrollArea && typeof modalScrollArea.scrollTo === 'function') {
                modalScrollArea.scrollTo({ top: modalScrollArea.scrollHeight, behavior: 'smooth' });
            }
        });
    }
};
const handleDropdownStateChange = (isOpen: boolean) => {
    isAdvancedSelectDropdownOpen.value = isOpen;
};

const selectLevelType = (type: 'Tahun' | 'Tingkatan') => {
    if (selectedLevelType.value === type) { // Deselect if clicking the same button
        selectedLevelType.value = null;
        formData.value.class = null;
    } else {
        selectedLevelType.value = type;
        formData.value.class = null; // Reset class selection when type changes
    }
    // Reset other form fields
    formData.value.className = '';
    formData.value.subject = null;
    formData.value.subjectAbbreviation = ''; // Reset abbreviation when level type changes
    formData.value.studentCount = null;
    formErrors.value = null;
    showNewSubjectInput.value = false;
    newSubjectNameFromInput.value = '';
    selectedExistingClassName.value = null;
};

const resetForm = () => {
    const currentClassSelection = selectedLevelType.value ? formData.value.class : null;
    formData.value = {
        id: null,
        class: currentClassSelection,
        className: '',
        studentCount: null,
        subject: null,
        subjectAbbreviation: '', // Clear abbreviation for new entries
    };
    editingId.value = null;
    isEditing.value = false;
    formErrors.value = null;
    showNewSubjectInput.value = false;
    newSubjectNameFromInput.value = '';
    selectedExistingClassName.value = null;
};

const openModalForNew = () => {
    isEditing.value = false;
    editingId.value = null;
    const currentLevel = selectedLevelType.value;
    const currentClassSel = formData.value.class;
    resetForm(); // Resets form fields
    selectedLevelType.value = currentLevel; // Restore level
    if (currentLevel) formData.value.class = currentClassSel; // Restore class if level was set
    isModalOpen.value = true;
};

const cancelModal = () => {
    isModalOpen.value = false;
    const currentLevel = selectedLevelType.value;
    const currentClass = formData.value.class;
    resetForm(); // Clear form
    selectedLevelType.value = currentLevel; // Preserve level type
    formData.value.class = currentClass; // Preserve class selection
};

const validateField = (fieldName: keyof Omit<ClassSubjectEntryForm, 'id'>) => {
    if (fieldName === 'subject' && formData.value.subject === ADD_NEW_SUBJECT_VALUE) {
        if (formErrors.value) formErrors.value.subject = undefined; // Clear error for "Add New"
        return;
    }

    let valueToParse: any = formData.value[fieldName];
    if (fieldName === 'studentCount') {
        const rawValue = formData.value[fieldName]; // rawValue is number | null
        // If rawValue is null or undefined, valueToParse will be null.
        // If it's a number, valueToParse will be that number.
        // Zod will handle if it's not a valid number for the schema (e.g. not positive).
        valueToParse = rawValue;
    }
    if (fieldName === 'subject' && typeof valueToParse === 'number') {
        valueToParse = String(valueToParse); // Zod expects string for subject ID
    }

    const fieldSchema = ClassSubjectEntrySchema.pick({ [fieldName]: true } as { [K in typeof fieldName]: true });
    const result = fieldSchema.safeParse({ [fieldName]: valueToParse });

    if (!formErrors.value) formErrors.value = {} as Record<keyof Omit<ClassSubjectEntryForm, 'id'>, string[] | undefined>;

    if (!result.success) {
        formErrors.value[fieldName] = result.error.flatten().fieldErrors[fieldName];
    } else {
        if (formErrors.value) formErrors.value[fieldName] = undefined;
    }

    const overallFormValid = !Object.values(formErrors.value || {}).some(errors => errors && errors.length > 0);
    const fullFormDataForValidation = {
        ...formData.value,
        subject: typeof formData.value.subject === 'number' ? String(formData.value.subject) : formData.value.subject
    };
    if (overallFormValid && ClassSubjectEntrySchema.safeParse(fullFormDataForValidation).success) {
        emit('data-valid');
    } else {
        emit('data-invalid');
    }
};

const validateForm = (): boolean => {
    if (formData.value.subject === ADD_NEW_SUBJECT_VALUE) {
        if (!formErrors.value) formErrors.value = {} as Record<keyof Omit<ClassSubjectEntryForm, 'id'>, string[] | undefined>;
        formErrors.value.subject = ["Sila simpan subjek baru atau pilih subjek sedia ada."];
        nextTick(() => document.getElementById('new-subject-input')?.focus());
        emit('data-invalid');
        return false;
    }
    const dataToValidate = {
        ...formData.value,
        subject: typeof formData.value.subject === 'number' ? String(formData.value.subject) : formData.value.subject,
    };
    const result = ClassSubjectEntrySchema.safeParse(dataToValidate);
    if (!result.success) {
        formErrors.value = result.error.flatten().fieldErrors as Record<keyof Omit<ClassSubjectEntryForm, 'id'>, string[] | undefined>;
        emit('data-invalid');
        return false;
    }
    formErrors.value = null;
    emit('data-valid');
    return true;
};

// Form Input Handlers
const handleClassSelection = (value: string | null) => {
    formData.value.class = value;
    selectedExistingClassName.value = null; // Reset existing class name selection
    if (value && !isEditing.value) formData.value.className = ''; // Clear className if not editing
    validateField('class');
    if (!value) { // If class is deselected, reset dependent fields
        formData.value.className = '';
        formData.value.subject = null;
        formData.value.studentCount = null;
        showNewSubjectInput.value = false;
        newSubjectNameFromInput.value = '';
    }
    if (formErrors.value?.subject) {
        formErrors.value.subject = undefined;
    }
};

const handleExistingClassSelection = (value: string | null) => {
    selectedExistingClassName.value = value;
    if (value) {
        formData.value.className = value;
        validateField('className');
        // Optionally, pre-fill student count if this class name exists in savedClassSubjects
        const existingEntry = savedClassSubjects.value.find(
            item => item.class === formData.value.class && item.className === value
        );
        if (existingEntry) {
            formData.value.studentCount = existingEntry.studentCount ?? null;
            validateField('studentCount');
        } else {
            formData.value.studentCount = null; // Reset if no matching entry
        }
    } else {
        // If "Kelas sedia ada" is cleared, user might type a new name.
        // Do not clear formData.className here. Reset student count.
        formData.value.studentCount = null;
        validateField('studentCount');
    }
};

const handleStudentCountInput = (value: string | number | null) => {
    // v-model.number provides number or null. If it were text, parse.
    formData.value.studentCount = value === '' ? null : (typeof value === 'string' ? Number(value) : value);
    validateField('studentCount');
};

const handleClassNameInput = (value: string) => {
    formData.value.className = value;
    validateField('className');
    // If user types a name that matches an existing one, could auto-select from "Kelas sedia ada"
    // Or if they type a new name after selecting an existing one, clear selectedExistingClassName
    if (selectedExistingClassName.value && value !== selectedExistingClassName.value) {
        // selectedExistingClassName.value = null; // Optional: clear if diverging
    }
};

const handleSubjectSelection = (value: string | null) => { // value from SingleSelect is string (UUID or ADD_NEW_SUBJECT_VALUE)
    if (value === ADD_NEW_SUBJECT_VALUE) {
        showNewSubjectInput.value = true;
        // Do not set formData.subject here, wait for new subject to be added or input cancelled
        // Clear any previous subject validation error if user opts to add new
        if (formErrors.value && formErrors.value.subject) {
            delete formErrors.value.subject;
        }
        nextTick(() => {
            const newSubjectInputEl = document.getElementById('new-subject-name-input');
            if (newSubjectInputEl) {
                newSubjectInputEl.focus();
            }
        });
    } else {
        formData.value.subject = value; // Store actual ID as string (UUID or null)

        // Auto-fill abbreviation if field is empty and we have a previous abbreviation for this subject
        if (value && !formData.value.subjectAbbreviation && subjectAbbreviationLookup.value.has(value)) {
            const existingAbbreviation = subjectAbbreviationLookup.value.get(value);
            if (existingAbbreviation) {
                formData.value.subjectAbbreviation = existingAbbreviation;
                // Validate the auto-filled abbreviation
                validateField('subjectAbbreviation');
            }
        }

        showNewSubjectInput.value = false;
        newSubjectNameFromInput.value = '';
        validateField('subject');
    }
    if (formErrors.value?.subject) {
        formErrors.value.subject = undefined;
    }
};

const handleSubjectAbbreviationInput = (value: string) => {
    formData.value.subjectAbbreviation = value;
    validateField('subjectAbbreviation');
};

const addNewSubject = async () => {
    const name = newSubjectNameFromInput.value.trim();
    if (!name) return;

    // Check if subject with the same name already exists (in DB or session-only)
    const combinedSubjects = [...subjects.value, ...sessionOnlySubjects.value];
    const existingSubject = combinedSubjects.find(s => s.name.toLowerCase() === name.toLowerCase());

    if (existingSubject) {
        if (!formErrors.value) {
            formErrors.value = { class: undefined, className: undefined, studentCount: undefined, subject: undefined, subjectAbbreviation: undefined };
        }
        formErrors.value.subject = ["Subjek dengan nama ini sudah wujud."];
        emit('data-invalid');
        nextTick(() => {
            document.getElementById('new-subject-input')?.focus();
        });
        return;
    }

    try {
        // Generate a simple code from the name
        const code = name.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .substring(0, 10); // Limit to 10 characters

        // Create subject in database using useSubjects composable
        const newSubject = await addSubjectToDb({
            name: name,
            code: code
        });

        if (!newSubject) {
            throw new Error('Failed to create subject');
        }

        // Update form data to select the newly created subject
        formData.value.subject = newSubject.id; // Use the actual UUID from database
        showNewSubjectInput.value = false;
        newSubjectNameFromInput.value = '';

        // Clear any previous subject validation errors
        if (formErrors.value && formErrors.value.subject) {
            delete formErrors.value.subject;
        }
        validateField('subject');

        console.log('Successfully created and selected new subject:', newSubject);

        nextTick(() => {
            const subjectSelectElement = document.querySelector('#subject-select button');
            if (subjectSelectElement instanceof HTMLElement) {
                subjectSelectElement.focus();
            }
        });

    } catch (error) {
        console.error('Error creating new subject:', error);

        if (!formErrors.value) {
            formErrors.value = { class: undefined, className: undefined, studentCount: undefined, subject: undefined, subjectAbbreviation: undefined };
        }
        formErrors.value.subject = ["Gagal mencipta subjek baru. Sila cuba lagi."];
        emit('data-invalid');

        nextTick(() => {
            document.getElementById('new-subject-input')?.focus();
        });
    }
};

// Table Data Management & Emitting
const saveClassSubject = () => {
    if (!validateForm()) {
        nextTick(() => { // Focus first error
            if (formErrors.value) {
                const firstErrorField = Object.keys(formErrors.value).find(key => formErrors.value![key as keyof Omit<ClassSubjectEntryForm, 'id'>]);
                if (firstErrorField) {
                    let elId = '';
                    if (firstErrorField === 'class') elId = 'class-select';
                    else if (firstErrorField === 'className') elId = 'class-name-input';
                    else if (firstErrorField === 'studentCount') elId = 'student-count-input';
                    else if (firstErrorField === 'subject') {
                        elId = formData.value.subject === ADD_NEW_SUBJECT_VALUE ? 'new-subject-input' : 'subject-select';
                    }
                    if (elId.includes('-select')) {
                        (document.querySelector(`#${elId} button`) as HTMLElement)?.focus();
                    } else {
                        document.getElementById(elId)?.focus();
                    }
                }
            }
        });
        return;
    }

    // Check for duplicate class-subject combination
    // Must check class, className, and subject to properly distinguish between different class sections
    const isDuplicate = savedClassSubjects.value.some(item =>
        item.class === formData.value.class &&
        item.className === formData.value.className &&
        item.subject === formData.value.subject &&
        (!isEditing.value || item.id !== formData.value.id) // Exclude current item if editing
    );

    if (isDuplicate) {
        if (!formErrors.value) formErrors.value = {} as Record<keyof Omit<ClassSubjectEntryForm, 'id'>, string[] | undefined>;
        formErrors.value.subject = ["Kombinasi kelas dan subjek ini sudah wujud."];
        emit('data-invalid');
        nextTick(() => {
            const subjectSelectElement = document.querySelector('#subject-select button');
            if (subjectSelectElement instanceof HTMLElement) {
                subjectSelectElement.focus();
            }
        });
        return;
    }

    const dataToSave: InternalClassSubject = {
        id: isEditing.value && editingId.value !== null ? editingId.value : nextInternalId++,
        class: formData.value.class!,
        className: formData.value.className,
        studentCount: formData.value.studentCount,
        subject: formData.value.subject!, // subject is already a string (UUID or ADD_NEW_SUBJECT_VALUE)
        subjectAbbreviation: formData.value.subjectAbbreviation,
    };

    if (isEditing.value) {
        const index = savedClassSubjects.value.findIndex(item => item.id === dataToSave.id);
        if (index !== -1) savedClassSubjects.value[index] = dataToSave;
    } else {
        savedClassSubjects.value.push(dataToSave);
    }
    isModalOpen.value = false;
    const currentLevel = selectedLevelType.value;
    const currentClass = formData.value.class;
    resetForm();
    selectedLevelType.value = currentLevel;
    formData.value.class = currentClass;
};

const handleEdit = (itemToEdit: InternalClassSubject) => {
    const isTahun = tahunOptions.some(opt => opt.value === itemToEdit.class);
    const isTingkatan = tingkatanOptions.some(opt => opt.value === itemToEdit.class);

    if (isTahun) selectedLevelType.value = 'Tahun';
    else if (isTingkatan) selectedLevelType.value = 'Tingkatan';
    else selectedLevelType.value = null;

    nextTick(() => { // Ensure classOptions are updated before setting formData
        formData.value = {
            id: itemToEdit.id, // Internal ID for editing tracking
            class: itemToEdit.class,
            className: itemToEdit.className,
            studentCount: itemToEdit.studentCount ?? null,
            subject: itemToEdit.subject, // subject is already a string (UUID)
            subjectAbbreviation: itemToEdit.subjectAbbreviation, // Preserve existing abbreviation when editing
        };
        isEditing.value = true;
        editingId.value = itemToEdit.id; // Store internal ID of item being edited
        formErrors.value = null;
        showNewSubjectInput.value = false;
        newSubjectNameFromInput.value = '';
        selectedExistingClassName.value = itemToEdit.className; // Pre-select if className exists
        isModalOpen.value = true;
    });
};

const handleDelete = (idToDelete: number) => { // idToDelete is internal ID
    // Confirmation dialog could be added here
    savedClassSubjects.value = savedClassSubjects.value.filter(item => item.id !== idToDelete);
};

// Helper to display class level labels in table
const getClassLevelLabel = (classValue: string | null): string => {
    if (!classValue) return '';
    const allClassOptions = [...tahunOptions, ...tingkatanOptions];
    const option = allClassOptions.find(opt => opt.value === classValue);
    return option ? option.label : String(classValue); // Fallback to value if not found
};

const getSubjectNameById = (subjectIdValue: string | null): string => {
    if (subjectIdValue === null || subjectIdValue === undefined || subjectIdValue === ADD_NEW_SUBJECT_VALUE) return '';

    const combinedSubjects = [...subjects.value, ...sessionOnlySubjects.value];
    const subject = combinedSubjects.find(s => s.id === subjectIdValue);

    if (subject) {
        return subject.name;
    }

    // If subjects are still loading, show loading indicator
    if (subjectsLoading.value) {
        return 'Memuatkan...';
    }

    // If no subject found and not loading, it might be an old ID from before migration
    // For now, show a clear message that the subject needs to be re-selected
    return `⚠️ Sila pilih semula subjek ini`;
};

// Watch savedClassSubjects to emit updates to parent
watch(savedClassSubjects, (newValue) => {
    const dataToEmit: EmittedClassSubject[] = newValue.map(item => {
        return {
            class_id: item.class!,
            className: item.className,
            subject_id: item.subject!, // All subjects now have valid UUIDs from database
            studentCount: item.studentCount,
            subject_abbreviation: item.subjectAbbreviation,
        };
    });

    emit('update:data', dataToEmit);

    // Validate that all emitted items have valid subject_ids
    const allSubjectsValid = dataToEmit.every(item =>
        item.subject_id !== null &&
        item.subject_id !== undefined &&
        typeof item.subject_id === 'string' &&
        item.subject_id.length > 0
    );

    if (allSubjectsValid && dataToEmit.length > 0) {
        emit('data-valid');
    } else if (dataToEmit.length === 0) {
        // Empty list can be considered valid depending on requirements
        emit('data-valid');
    } else {
        emit('data-invalid');
    }

}, { deep: true });

// Watch for selectedLevelType changes to reset class if it becomes invalid for new options
watch(selectedLevelType, () => {
    const currentClassIsValid = classOptions.value.some(opt => opt.value === formData.value.class);
    if (!currentClassIsValid) {
        formData.value.class = null; // Reset if current class not in new options
    }
});

</script>

<style scoped>
/* Add any specific styles if needed */
</style>
