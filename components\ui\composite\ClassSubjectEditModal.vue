<template>
    <UiCompositeModal :is-open="isOpen" title="Kemaskini Kelas & Subjek"
        @update:is-open="$emit('update:is-open', $event)">
        <form @submit.prevent="saveClassSubject" class="space-y-4 p-4">
            <div class="flex space-x-2 mb-4">
                <UiBaseButton @click="selectLevelType('Tahun')"
                    :variant="selectedLevelType === 'Tahun' ? 'primary' : 'outline'" type="button" class="flex-1">
                    <PERSON>hun
                </UiBaseButton>
                <UiBaseButton @click="selectLevelType('Tingkatan')"
                    :variant="selectedLevelType === 'Tingkatan' ? 'primary' : 'outline'" type="button" class="flex-1">
                    Tingkatan
                </UiBaseButton>
            </div>

            <div>
                <UiBaseSingleSelect id="class-select" v-model="formData.class" :options="classOptions"
                    option-label="label" option-value="value" placeholder="Sila pilih kelas"
                    @update:modelValue="handleClassSelection" :aria-invalid="!!formErrors?.class"
                    aria-describedby="class-error" :disabled="isFormDisabled" />
                <span v-if="formErrors?.class" id="class-error" class="text-alert-error text-sm mt-1">{{
                    formErrors.class[0] }}</span>
            </div>

            <div v-if="formData.class && existingClassOptions.length > 0">
                <UiBaseSingleSelect id="existing-class-select" v-model="selectedExistingClassName"
                    :options="existingClassOptions" option-label="label" option-value="value"
                    placeholder="Pilih nama kelas sedia ada (jika ada)"
                    @update:modelValue="handleExistingClassSelection" :disabled="isFormDisabled || !formData.class" />
            </div>

            <div>
                <UiBaseInput id="class-name-input" v-model="formData.className"
                    placeholder="Nama Kelas (Cth: Tahun 1, 2 Bestari)" @update:modelValue="handleClassNameInput"
                    :aria-invalid="!!formErrors?.className" aria-describedby="className-error"
                    :disabled="isFormDisabled" />
                <span v-if="formErrors?.className" id="className-error" class="text-alert-error text-sm mt-1">{{
                    formErrors.className[0] }}</span>
            </div>

            <div>
                <UiBaseInput id="student-count-input" v-model.number="formData.studentCount" type="number"
                    placeholder="Jumlah Murid" @update:modelValue="handleStudentCountInput"
                    :aria-invalid="!!formErrors?.studentCount" aria-describedby="studentCount-error"
                    :disabled="isFormDisabled" />
                <span v-if="formErrors?.studentCount" id="studentCount-error" class="text-alert-error text-sm mt-1">{{
                    formErrors.studentCount[0] }}</span>
            </div>

            <div>
                <UiBaseSingleSelect id="subject-select" v-model="formData.subject" :options="computedSubjectOptions"
                    option-label="label" option-value="value" show-search
                    :placeholder="!formData.class ? 'Pilih Kelas dahulu' : 'Sila pilih subjek'"
                    @update:modelValue="handleSubjectSelection" :aria-invalid="!!formErrors?.subject"
                    aria-describedby="subject-error" :disabled="isFormDisabled || subjectsLoading || !formData.class">
                    <template #customOptionLabel="{ option }">
                        <div v-if="option.value === ADD_NEW_SUBJECT_VALUE" class="flex items-center">
                            <UiBaseIcon name="mdi:plus" class="mr-2 h-5 w-5" />
                            <span>Tambah Subjek Baru...</span>
                        </div>
                    </template>
                </UiBaseSingleSelect>
                <span v-if="formErrors?.subject && formData.subject !== ADD_NEW_SUBJECT_VALUE" id="subject-error"
                    class="text-alert-error text-sm mt-1">{{
                        formErrors.subject[0] }}</span>
            </div>

            <div v-if="showNewSubjectInput" class="space-y-2 mt-2">
                <UiBaseInput id="new-subject-input" v-model="newSubjectNameFromInput"
                    placeholder="Taip nama subjek baru" :disabled="isFormDisabled || subjectsLoading" />
                <UiBaseButton @click="addNewSubject" variant="secondary" type="button"
                    :disabled="isFormDisabled || subjectsLoading || !newSubjectNameFromInput.trim()">
                    Simpan Subjek Baru
                </UiBaseButton>
                <span v-if="formErrors?.subject && formData.subject === ADD_NEW_SUBJECT_VALUE" id="new-subject-error"
                    class="text-alert-error text-sm mt-1">{{ formErrors.subject[0] }}</span>
            </div>

            <div>
                <UiBaseInput id="subject-abbreviation-input" v-model="formData.subjectAbbreviation"
                    placeholder="Singkatan nama subjek (cth: BM, BI, MAT)"
                    @update:modelValue="handleSubjectAbbreviationInput"
                    :aria-invalid="!!formErrors?.subjectAbbreviation" aria-describedby="subjectAbbreviation-error"
                    :disabled="isFormDisabled" />
                <span v-if="formErrors?.subjectAbbreviation" id="subjectAbbreviation-error"
                    class="text-alert-error text-sm mt-1">{{
                        formErrors.subjectAbbreviation[0] }}</span>
            </div>
        </form>

        <template #footer>
            <div class="flex justify-end space-x-3">
                <UiBaseButton variant="outline" @click="cancelModal">
                    Batal
                </UiBaseButton>
                <UiBaseButton variant="primary" prepend-icon="mdi:check" @click="saveClassSubject"
                    :disabled="isFormDisabled || subjectsLoading || !selectedLevelType || !isFormValid">
                    {{ isSaving ? 'Menyimpan...' : 'Kemaskini' }}
                </UiBaseButton>
            </div>
        </template>
    </UiCompositeModal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { z } from 'zod'
import { useSubjects } from '@/composables/useSubjects'
import type { UserClassSubjectEntry } from '~/schemas/userSchemas'

// Props and Emits
const props = defineProps<{
    isOpen: boolean
    initialData?: UserClassSubjectEntry | null
}>()

const emit = defineEmits<{
    'update:is-open': [value: boolean]
    'save': [data: UserClassSubjectEntry]
}>()

// Types
interface FormOption {
    value: string
    label: string
    code?: string
    disabled?: boolean
}

interface ClassSubjectFormData {
    class: string | null
    className: string
    studentCount: number | null
    subject: string | null
    subjectAbbreviation: string
}

// State
const selectedLevelType = ref<'Tahun' | 'Tingkatan' | null>(null)
const formData = ref<ClassSubjectFormData>({
    class: null,
    className: '',
    studentCount: null,
    subject: null,
    subjectAbbreviation: '',
})

const isSaving = ref(false)
const formErrors = ref<Record<string, string[]> | null>(null)

// Subject related state
const ADD_NEW_SUBJECT_VALUE = 'add_new_subject_option_value'
const newSubjectNameFromInput = ref('')
const showNewSubjectInput = ref(false)
const { subjects, loading: subjectsLoading, fetchSubjects, addSubject: addSubjectToDb } = useSubjects()

// "Kelas sedia ada" state
const selectedExistingClassName = ref<string | null>(null)

// Options
const tahunOptions: FormOption[] = [
    { value: 't1', label: 'Tahun 1' }, { value: 't2', label: 'Tahun 2' }, { value: 't3', label: 'Tahun 3' },
    { value: 't4', label: 'Tahun 4' }, { value: 't5', label: 'Tahun 5' }, { value: 't6', label: 'Tahun 6' },
]

const tingkatanOptions: FormOption[] = [
    { value: 'f1', label: 'Tingkatan 1' }, { value: 'f2', label: 'Tingkatan 2' }, { value: 'f3', label: 'Tingkatan 3' },
    { value: 'f4', label: 'Tingkatan 4' }, { value: 'f5', label: 'Tingkatan 5' }, { value: 'f6', label: 'Tingkatan 6' },
]

const CATEGORY_LABELS: Record<string, string> = {
    'teras': 'Teras',
    'tambahan': 'Tambahan',
    'pilihan': 'Pilihan',
    'elektif_sains': 'Elektif Sains',
    'elektif_sastera': 'Elektif Sastera'
}

// Computed
const classOptions = computed<FormOption[]>(() => {
    if (selectedLevelType.value === 'Tahun') return tahunOptions
    if (selectedLevelType.value === 'Tingkatan') return tingkatanOptions
    return []
})

const isFormDisabled = computed(() => !selectedLevelType.value || subjectsLoading.value)

// Helper function to get class level from class value
const getClassLevel = (classValue: string): string | null => {
    if (!classValue) return null
    if (classValue.startsWith('t')) return 'tahun'
    if (classValue.startsWith('f')) {
        const level = parseInt(classValue.substring(1))
        if (level >= 1 && level <= 3) return 'lower'
        if (level >= 4 && level <= 6) return 'upper'
    }
    return null
}

const computedSubjectOptions = computed<FormOption[]>(() => {
    let combinedSubjects = [...subjects.value]

    // Filter subjects based on selected level and class
    if (selectedLevelType.value && formData.value.class) {
        const levelType = selectedLevelType.value.toLowerCase()
        const classLevel = getClassLevel(formData.value.class)

        combinedSubjects = combinedSubjects.filter(subject => {
            if (subject.is_custom) return true
            if (subject.level_type !== levelType && subject.level_type !== 'both') return false
            if (levelType === 'tingkatan' && classLevel && subject.sub_level) {
                if (subject.sub_level !== 'all' && subject.sub_level !== classLevel) return false
            }
            return subject.is_active !== false
        })
    }

    // Group subjects by category
    const groupedSubjects = combinedSubjects.reduce((groups, subject) => {
        const category = subject.category || 'lain-lain'
        if (!groups[category]) groups[category] = []
        groups[category].push(subject)
        return groups
    }, {} as Record<string, typeof combinedSubjects>)

    // Sort subjects within each category
    Object.keys(groupedSubjects).forEach(category => {
        groupedSubjects[category].sort((a, b) => {
            if (a.sort_order !== b.sort_order) {
                return (a.sort_order || 999) - (b.sort_order || 999)
            }
            return a.name.localeCompare(b.name)
        })
    })

    // Create options with category headers
    const options: FormOption[] = []
    const categoryOrder = ['teras', 'tambahan', 'pilihan', 'elektif_sains', 'elektif_sastera', 'lain-lain']

    categoryOrder.forEach(category => {
        if (groupedSubjects[category] && groupedSubjects[category].length > 0) {
            options.push({
                value: `header_${category}`,
                label: `--- ${CATEGORY_LABELS[category] || category.toUpperCase()} ---`,
                code: '',
                disabled: true
            })

            groupedSubjects[category].forEach(subject => {
                options.push({
                    value: subject.id,
                    label: subject.name,
                    code: subject.code
                })
            })
        }
    })

    if (options.length > 0) {
        options.push({ value: ADD_NEW_SUBJECT_VALUE, label: '+ Tambah Subjek Baru...', code: '' })
    }

    return options
})

// Validation schema
const ClassSubjectEntrySchema = z.object({
    class: z.string().min(1, "Sila pilih kelas."),
    className: z.string().min(1, "Nama kelas diperlukan.").max(100, "Nama kelas terlalu panjang."),
    studentCount: z.number().int("Jumlah murid mesti nombor bulat.").positive("Jumlah murid mesti nombor positif."),
    subject: z.string().min(1, "Sila pilih subjek.").refine(value => value !== ADD_NEW_SUBJECT_VALUE, {
        message: "Sila simpan subjek baru atau pilih subjek sedia ada.",
    }),
    subjectAbbreviation: z.string().min(1, "Singkatan nama subjek diperlukan.").max(10, "Singkatan nama subjek terlalu panjang."),
})

const existingClassOptions = computed<FormOption[]>(() => {
    if (!formData.value.class) return []
    // For the edit modal, we don't have access to savedClassSubjects like in ClassSubject
    // So we'll return an empty array for now, but this could be enhanced to get data from props
    return []
})

const isFormValid = computed(() => {
    if (!formData.value.class || !formData.value.className || !formData.value.subject || !formData.value.subjectAbbreviation) {
        return false
    }
    if (formData.value.subject === ADD_NEW_SUBJECT_VALUE) return false
    if (formData.value.studentCount === null || formData.value.studentCount <= 0) return false
    return true
})

// Functions
const selectLevelType = (type: 'Tahun' | 'Tingkatan') => {
    if (selectedLevelType.value === type) {
        selectedLevelType.value = null
        formData.value.class = null
    } else {
        selectedLevelType.value = type
        formData.value.class = null
    }
    formData.value.className = ''
    formData.value.subject = null
    formData.value.subjectAbbreviation = ''
    formData.value.studentCount = null
    formErrors.value = null
    showNewSubjectInput.value = false
    newSubjectNameFromInput.value = ''
    selectedExistingClassName.value = null
}

const resetForm = () => {
    formData.value = {
        class: null,
        className: '',
        studentCount: null,
        subject: null,
        subjectAbbreviation: '',
    }
    selectedLevelType.value = null
    formErrors.value = null
    showNewSubjectInput.value = false
    newSubjectNameFromInput.value = ''
    selectedExistingClassName.value = null
}

const cancelModal = () => {
    emit('update:is-open', false)
    resetForm()
}

const validateField = (fieldName: keyof ClassSubjectFormData) => {
    if (fieldName === 'subject' && formData.value.subject === ADD_NEW_SUBJECT_VALUE) {
        if (formErrors.value && formErrors.value.subject) {
            delete formErrors.value.subject
        }
        return
    }

    const fieldSchema = ClassSubjectEntrySchema.pick({ [fieldName]: true } as { [K in typeof fieldName]: true })
    const result = fieldSchema.safeParse({ [fieldName]: formData.value[fieldName] })

    if (!formErrors.value) formErrors.value = {}

    if (!result.success) {
        formErrors.value[fieldName] = result.error.flatten().fieldErrors[fieldName] || []
    } else {
        if (formErrors.value && formErrors.value[fieldName]) {
            delete formErrors.value[fieldName]
        }
    }
}

const validateForm = (): boolean => {
    if (formData.value.subject === ADD_NEW_SUBJECT_VALUE) {
        if (!formErrors.value) formErrors.value = {}
        formErrors.value.subject = ["Sila simpan subjek baru atau pilih subjek sedia ada."]
        return false
    }

    const result = ClassSubjectEntrySchema.safeParse(formData.value)
    if (!result.success) {
        formErrors.value = result.error.flatten().fieldErrors as Record<string, string[]>
        return false
    }

    formErrors.value = null
    return true
}

const handleClassSelection = (value: string | null) => {
    formData.value.class = value
    selectedExistingClassName.value = null // Reset existing class name selection
    if (!value) {
        formData.value.className = ''
        formData.value.subject = null
        formData.value.studentCount = null
        showNewSubjectInput.value = false
        newSubjectNameFromInput.value = ''
    }
    validateField('class')
}

const handleExistingClassSelection = (value: string | null) => {
    selectedExistingClassName.value = value
    if (value) {
        formData.value.className = value
        validateField('className')
        // Note: In the original ClassSubject, this would also pre-fill student count
        // but since we don't have access to savedClassSubjects here, we skip that
    }
}

const handleClassNameInput = (value: string) => {
    formData.value.className = value
    validateField('className')
}

const handleStudentCountInput = (value: string | number | null) => {
    formData.value.studentCount = value === '' ? null : (typeof value === 'string' ? Number(value) : value)
    validateField('studentCount')
}

const handleSubjectSelection = (value: string | null) => {
    if (value === ADD_NEW_SUBJECT_VALUE) {
        showNewSubjectInput.value = true
        if (formErrors.value && formErrors.value.subject) {
            delete formErrors.value.subject
        }
        nextTick(() => {
            document.getElementById('new-subject-input')?.focus()
        })
    } else {
        formData.value.subject = value
        showNewSubjectInput.value = false
        newSubjectNameFromInput.value = ''
        validateField('subject')
    }
}

const handleSubjectAbbreviationInput = (value: string) => {
    formData.value.subjectAbbreviation = value
    validateField('subjectAbbreviation')
}

const addNewSubject = async () => {
    const name = newSubjectNameFromInput.value.trim()
    if (!name) return

    const existingSubject = subjects.value.find(s => s.name.toLowerCase() === name.toLowerCase())
    if (existingSubject) {
        if (!formErrors.value) formErrors.value = {}
        formErrors.value.subject = ["Subjek dengan nama ini sudah wujud."]
        return
    }

    try {
        const code = name.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-')
            .substring(0, 10)

        const newSubject = await addSubjectToDb({
            name: name,
            code: code
        })

        if (!newSubject) {
            throw new Error('Failed to create subject')
        }

        formData.value.subject = newSubject.id
        showNewSubjectInput.value = false
        newSubjectNameFromInput.value = ''

        if (formErrors.value && formErrors.value.subject) {
            delete formErrors.value.subject
        }
        validateField('subject')
    } catch (error) {
        console.error('Error creating new subject:', error)
        if (!formErrors.value) formErrors.value = {}
        formErrors.value.subject = ["Gagal mencipta subjek baru. Sila cuba lagi."]
    }
}

const saveClassSubject = async () => {
    if (!validateForm()) return

    isSaving.value = true

    try {
        const dataToSave: UserClassSubjectEntry = {
            class_id: formData.value.class!,
            className: formData.value.className,
            subject_id: formData.value.subject!,
            studentCount: formData.value.studentCount!,
            subject_abbreviation: formData.value.subjectAbbreviation,
        }

        emit('save', dataToSave)
        emit('update:is-open', false)
        resetForm()
    } catch (error) {
        console.error('Error saving class subject:', error)
    } finally {
        isSaving.value = false
    }
}

// Initialize form when modal opens with initial data
watch(() => props.isOpen, (isOpen) => {
    if (isOpen && props.initialData) {
        const data = props.initialData

        // Determine level type from class_id
        if (data.class_id.startsWith('t')) {
            selectedLevelType.value = 'Tahun'
        } else if (data.class_id.startsWith('f')) {
            selectedLevelType.value = 'Tingkatan'
        }

        // Set form data
        formData.value = {
            class: data.class_id,
            className: data.className,
            studentCount: data.studentCount || null,
            subject: data.subject_id,
            subjectAbbreviation: data.subject_abbreviation || '',
        }

        formErrors.value = null
        showNewSubjectInput.value = false
        newSubjectNameFromInput.value = ''
        selectedExistingClassName.value = data.className // Pre-select if className exists
    } else if (!isOpen) {
        resetForm()
    }
})

// Fetch subjects on mount
onMounted(async () => {
    await fetchSubjects()
})
</script>
